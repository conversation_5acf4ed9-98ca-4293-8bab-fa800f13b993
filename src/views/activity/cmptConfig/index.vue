<template>
  <div class="app-container">
    <div>
      <el-container>
        <el-header class="header">
          <el-row>
            <el-form :inline="true" :model="searchForm">
              <el-form-item label="活动id">
                <activity-selector v-model="searchForm.actId" style="width:300px" clearable />
              </el-form-item>

              <el-form-item label="组件id">
                <el-select v-model="searchForm.componentId" placeholder="请选择组件" filterable clearable
                           style="width:300px">
                  <el-option v-for="item in selectComponents" :value="item.code" :key="item.code"
                             :label='item.desc+"("+item.code+")"'>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="模块名称">
                <el-input v-model="searchForm.moduleName" clearable placeholder="需要配合活动id一起使用" style="width:200px"></el-input>
              </el-form-item>

              <el-button type="primary" :loading="loadingCmpt" @click="onSearch" icon="el-icon-search" size="medium">查询</el-button>
              <el-button type="primary" icon="el-icon-plus" size="medium" @click="onAddComponent">添加组件</el-button>
              <el-button type="primary" icon="el-icon-document-copy" size="medium" @click="multiCopyCmpt">复制多个组件</el-button>
              <el-button type="primary" icon="el-icon-upload" size="medium" @click="$refs.jsonConfigModal.showModal('', '导入JSON配置')">导入JSON</el-button>
            </el-form>
          </el-row>
        </el-header>

        <el-main>
          <el-row v-for="(items,index) in actComponents" :key="index">
            <el-col :span="6" v-for="(actComponent,index) in items" :key="index">
              <el-card :body-style="{ padding: '10px 15px'}" style="margin-right: 10px;margin-bottom: 10px;">
                <!-- -->
                <div slot="header" style="display: flex; justify-content: space-between; align-items: center;">
                  <div>
                    <!-- <el-button class="button" @click="handleCmptConfigVisualize(actComponent)" type="text">组件验证
                    </el-button>-->
                    <el-button class="button" @click="handleCmptAttrConfig(actComponent)" type="text">配置属性</el-button>
                  </div>
                  <div>
                    <el-button class="button" type="text" @click="onEditComponent(actComponent)" style="color: #409EFF;">编辑</el-button>
                    <el-button class="button" type="text" @click="onCopyComponent(actComponent)" style="color: #67c23a;">复制</el-button>
<!--                    <el-button v-if="autotestVisible && actComponent.autotestTaskId > 0" @click="onAutotest(actComponent)" class="button" type="text" style="color: yellowgreen;">测试</el-button>-->
                    <el-popconfirm style="margin-left: 10px;"
                                   confirm-button-text="确认"
                                   cancel-button-text="取消"
                                   @confirm="onDeleteComponent(actComponent)"
                                   title="确定删除该组件吗?">
                      <el-button slot="reference" class="button" type="text" style="color: #f56c6c;">删除</el-button>
                    </el-popconfirm>
                    <el-button v-if="autotestVisible" @click="onSynCmptAttrDefine(actComponent)" class="button" type="text" style="color: #E6A23C;">属性定义</el-button>
                  </div>
                </div>

                <div style="font-size: 14px;margin-bottom: 12px;">组件id：{{actComponent.componentId}}</div>
                <div style="font-size: 14px;margin-bottom: 12px;">组件名称：{{actComponent.componentTitle}}
                  <el-tooltip effect="light" placement="right-start"
                              style="margin-left: 10px;" v-if="actComponent.remark !== '' && actComponent.remark !== null">
                    <div slot="content">{{ actComponent.remark }}</div>
                    <i class="el-icon-question"></i>
                  </el-tooltip>
                </div>
                <!--
                <span style="color: #67c23a">{{actComponent.componentId}}</span> - <span>{{actComponent.componentTitle}}</span>
                -->
                <div style="font-size: 14px;margin-bottom: 12px;">
                  活动id：<span>{{getActName(actComponent.actId)}} -- </span>{{actComponent.actId}}
                </div>
                <!-- -->
                <div style="font-size: 14px;margin-bottom: 12px;">组件序号：{{actComponent.componentUseIndex}}</div>

                <div style="font-size: 14px;margin-bottom: 12px;">模块名称：{{actComponent.moduleName}}</div>
              </el-card>
            </el-col>
          </el-row>
        </el-main>

        <el-dialog title="复制多个组件" :close-on-click-modal="false" :visible.sync="multiCopyVisible">
          <el-row>
            <el-form :inline="true">
              <el-form-item label="活动id">
                <activity-selector v-model="queryActIdList" style="width:750px" multiple clearable />
              </el-form-item>
              <el-button type="primary" :loading="queryingCmpt" @click="queryCmptByActIds" icon="el-icon-search"
                         size="medium">查询
              </el-button>
            </el-form>
          </el-row>

          <el-table border ref="multipleTable" :data="multiCopyCmptList">
            <el-table-column type="selection" align="center" width="55"></el-table-column>
            <el-table-column property="cmptId" label="组件ID" align="center"></el-table-column>
            <el-table-column property="cmptTitle" label="组件名称" align="center"></el-table-column>
            <el-table-column property="cmptUseInx" label="组件序号" align="center"></el-table-column>
            <el-table-column property="actId" label="活动ID" align="center"></el-table-column>
            <el-table-column property="status" label="组件状态" align="center"></el-table-column>

            <!-- <el-table-column property="活动名" label="剩余还款" align="center"></el-table-column>
            <el-table-column property="stage_price" label="本期应还" align="center"></el-table-column>
            <el-table-column property="number" label="剩余期数" align="center"></el-table-column>-->
          </el-table>
          <activity-selector v-model="targetActId" style="width:350px" clearable />
          <el-button type="primary" @click="mutliCopying" icon="el-icon-document-copy"
                     size="medium">复制选中的组件
          </el-button>
        </el-dialog>


        <!-- 添加组件 -->
        <el-dialog title="新增组件" :close-on-click-modal="false" :visible.sync="addComponent">
          <el-form ref="actComponentData" :model="actComponentData" label-width="100px" size="medium">
            <el-row>
              <el-col :span="12">
                <el-form-item label="活动信息">
                  <activity-selector v-model="actComponentData.actId" style="width:300px" @change="actSelectChange" :disabled="actComponentData.editing" />
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="组件id">
                  <el-select v-model="actComponentData.componentId" placeholder="请选择组件" filterable style="width:300px" :disabled="actComponentData.editing">
                    <el-option v-for="item in selectComponents" :value="Number(item.code)" :key="item.code"
                               :label='item.desc+"("+item.code+")"'>
                    </el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="12">
                <el-form-item label="组件序号">
                  <el-input v-model="actComponentData.useIndex" placeholder="组件序号" :disabled="actComponentData.editing" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="模块名称">
                  <div style="display: flex; align-items: center;">
                    <el-input v-model="actComponentData.moduleName" placeholder="模块名称" style="flex: 1;"></el-input>
                    <span style="margin: 0 8px;">-</span>
                    <el-input v-model="actComponentData.subModuleName" placeholder="子模块名称" style="flex: 1;"></el-input>
                  </div>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-form-item label="扩展信息">
                <el-input type="textarea" v-model="actComponentData.extJson" placeholder="扩展信息"></el-input>
              </el-form-item>
            </el-row>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button @click="onCancelComponentDialog">取 消</el-button>
            <el-button type="primary" @click="handleSaveActComponent">保 存</el-button>
          </span>
        </el-dialog>

        <!-- 复制组件 -->
        <el-dialog title="复制组件" :visible.sync="copyVisible" width="30%"
                   :close-on-click-modal="false" @closed="copyVisible = false">
          <el-form :model="copyComponent" label-width="150px">

            <el-form-item label="活动id">
              <el-select v-model="copyComponent.fromActId" style="width:100%;" :disabled="true">
                <el-option v-for="item in selectActList" :value="item.act_id" :key="item.act_id"
                           :label='item.act_name+"("+item.act_id+")"'>
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="组件id">
              <el-select v-model="copyComponent.componentId" style="width:100%;" :disabled="true">
                <el-option v-for="item in selectComponents" :value="item.code" :key="item.code"
                           :label='item.desc+"("+item.code+")"'>
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="组件序号">
              <el-input :disabled="true" v-model="copyComponent.fromUseIndex"></el-input>
            </el-form-item>

            <el-form-item label="新活动id">
              <el-select v-model="copyComponent.toActId" placeholder="请选择新活动" filterable style="width:100%;"
                         @change="newActSelectChange">
                <el-option v-for="item in selectActList" :value="item.act_id" :key="item.act_id"
                           :label='item.act_name+"("+item.act_id+")"'>
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="新组件序号">
              <el-input v-model="copyComponent.toUseIndex"></el-input>
            </el-form-item>

            <el-form-item label="模块名称">
              <el-input v-model="copyComponent.moduleName" placeholder="模块名称"></el-input>
            </el-form-item>

            <el-form-item label="扩展信息">
              <el-input type="textarea" v-model="copyComponent.extJson" placeholder="扩展信息"></el-input>
            </el-form-item>
          </el-form>

          <div slot="footer" class="dialog-footer">
            <el-button @click="copyVisible = false">取 消</el-button>
            <el-button type="primary" @click="onSaveComponentCopy">保 存</el-button>
          </div>
        </el-dialog>

        <!-- 自动化测试 -->
<!--        <autotest ref="autotest" />-->
        <json-modal ref="jsonConfigModal" @submit="importJsonConfig" />
      </el-container>
    </div>


  </div>
</template>

<script>
  import Api from '@/api/activity/cmptConfig'
  import ActConfigVersion from "@/api/activity/actConfigVersion"
  // import autotest from './autotest'
  import ActivitySelector from "@/components/ActivitySelector"
  import JsonModal from "@/components/Json/JsonModal";

  export default {
    name: "CmptConfig",
    components: {
      ActivitySelector, JsonModal
    },
    data: function () {
      return {
        selectActList: [],
        loadingCmpt: false,
        searchForm: {
          actId: '',
          componentId: '',
          moduleName: ''
        },
        currentActName: '',
        actComponents: [],
        selectComponents: [],
        addComponent: false,
        actComponentData: {
          editing: false,
          actId: '',
          componentId: '',
          useIndex: '',
          moduleName: '',
          subModuleName: '',
          extJson: ''
        },
        copyVisible: false,
        copyComponent: {
          fromActId: '',
          componentId: '',
          fromUseIndex: '',
          toActId: '',
          toUseIndex: '',
          moduleName: '',
          extJson: ''
        },

        multiCopyVisible: false,
        queryActIdList: [],
        queryingCmpt: false,
        multiCopyCmptList: [],
        targetActId: '',
        autotestVisible: process.env.ENV !== 'production'
        }
    },
    created: function () {
      this.loadActInfos()
      this.loadComponentDefines()
    },
    methods: {
      //加载活动数据
      loadActInfos() {
        ActConfigVersion.loadActInfos().then(
          res => {
            if (res.result === 200) {
              this.selectActList = res.data
            } else {
              alert("拉取活动列表错误," + res.reason)
            }
          }
        )
      },
      // 加载组件数据
      loadComponentDefines() {
        Api.loadComponentDefines().then(
          res => {
            if (res.result === 200) {
              this.selectComponents = res.data
            } else {
              alert("拉取组件列表错误," + res.reason)
            }
          }
        )
      },
      onSearch() {
        const self = this
        if (self.searchForm.actId === '' && self.searchForm.componentId === '') {
          self.$Message.warning('请选择活动或组件')
          return
        }
        self.loadingCmpt = true
        Api.loadComponentConfig(self.searchForm.actId, self.searchForm.componentId, self.searchForm.moduleName).then(
          res => {
            if (res.result === 200) {
              self.actComponents = res.data
              if (self.actComponents.length === 0) {
                self.$Message.warning("没有查询到数据")
              }
            } else {
              self.$Message.warning(res.reason)
            }
            self.loadingCmpt = false
          }
        ).catch((error) => {
          self.loadingCmpt = false
          self.$Message.error(error.status + "," + error.statusText, 5)
        });
      },
      // 为活动添加组件
      onAddComponent() {
        this.addComponent = true
        this.resetActComponentData()
        this.actComponentData.editing = false
        this.actComponentData.actId = this.searchForm.actId
        this.getBusiId()
      },

      multiCopyCmpt() {
        this.multiCopyVisible = true;
        console.log('multiCopyCmpt click!')
      },

      // 关闭弹窗
      onCancelComponentDialog() {
        this.addComponent = false
        this.resetActComponentData()
      },
      resetActComponentData() {
        this.actComponentData = {
          actId: '',
          componentId: '',
          useIndex: '',
          moduleName: '',
          subModuleName: '',
          extJson: ''
        }
      },
      // 保存
      handleSaveActComponent() {
        const self = this
        const data = self.actComponentData
        if (data.actId === '') {
          self.$Message.warning('请选择活动')
          return
        }
        if (data.componentId === '') {
          self.$Message.warning('请选择组件')
          return
        }
        if (data.useIndex === '' || isNaN(data.useIndex)) {
          self.$Message.warning('请填写正确的组件序号')
          return
        }
        Api.saveActComponent(data).then(
          res => {
            if (res.result === 200) {
              self.$Message.success(res.data)
              self.addComponent = false
              self.searchForm.actId = self.actComponentData.actId
              self.onSearch()
            } else {
              self.$Message.warning(res.reason)
            }
          }
        ).catch((error) => {
          self.addComponent = false
          self.$Message.error(error.status + "," + error.statusText, 5)
        })
      },
      // 获取活动对应的业务Id
      getBusiId() {
        const actId = this.actComponentData.actId
        if (actId === '') {
          return
        }
        for (const element of this.selectActList) {
          if (element.act_id === actId) {
            this.actComponentData.useIndex = element.busiId
            break
          }
        }
      },
      actSelectChange() {
        this.getBusiId()
      },
      newActSelectChange() {
        const actId = this.copyComponent.toActId
        if (actId === '') {
          return
        }
        for (const element of this.selectActList) {
          if (element.act_id === actId) {
            this.copyComponent.toUseIndex = element.busiId
            break
          }
        }
      },
      getCurrentActName(actId) {
        if (actId === '') {
          return
        }
        for (const element of this.selectActList) {
          if (element.act_id === actId) {
            return element.act_name
          }
        }
      },
      getActName(actId) {
        console.log(actId)
        if (actId === '') {
          return
        }
        for (const element of this.selectActList) {
          if (element.act_id === actId) {
            return element.act_name
          }
        }
      },
      onDeleteComponent(item) {
        const self = this
        const data = {
          actId: item.actId,
          componentId: item.componentId,
          useIndex: item.componentUseIndex
        }
        Api.deleteActComponent(data).then(
          res => {
            if (res.result === 200) {
              self.$Message.success(res.data)
              self.onSearch()
            } else {
              self.$Message.warning(res.reason)
            }
          }
        ).catch((error) => {
          self.addComponent = false
          self.$Message.error(error.status + "," + error.statusText, 5)
        })
      },
      onEditComponent(item) {
        this.actComponentData = { ... item }
        if (item.moduleName && item.moduleName.includes('-')) {
          const parts = item.moduleName.split('-')
          this.actComponentData.moduleName = parts[0]
          this.actComponentData.subModuleName = parts[1]
        } else {
          this.actComponentData.moduleName = item.moduleName || ''
          this.actComponentData.subModuleName = ''
        }
        this.actComponentData.editing = true
        this.actComponentData.useIndex = item.componentUseIndex
        this.addComponent = true
      },
      // 组件复制
      onCopyComponent(item) {
        this.copyVisible = true
        this.copyComponent.fromActId = item.actId
        this.copyComponent.componentId = item.componentId + ''
        this.copyComponent.fromUseIndex = item.componentUseIndex
        this.copyComponent.extJson = ''
        this.copyComponent.moduleName = ''
        this.copyComponent.toActId = ''
        this.copyComponent.toUseIndex = ''
      },
      // 保存复制
      onSaveComponentCopy() {
        const self = this
        const data = self.copyComponent
        if (data.toActId === '') {
          self.$Message.warning('请选择新活动')
          return
        }
        if (data.toUseIndex === '' || isNaN(data.toUseIndex)) {
          self.$Message.warning('请填写正确的组件序号')
          return
        }

        Api.copyActComponent(this.copyComponent)
          .then(res => {
            if (res.result === 200) {
              self.$Message.success(res.data)
              self.copyVisible = false
              self.onSearch()
            } else {
              self.$Message.warning(res.reason)
            }
          })
      },
      /** 属性配置操作 */
      handleCmptAttrConfig: function (row) {
        const param = {
          actId: row.actId,
          componentId: row.componentId,
          useIndex: row.componentUseIndex,
          componentTitle: row.componentTitle,
          actName: this.getCurrentActName(row.actId)
        }
        this.$router.push({path: 'cmptAttrConfig', query: param});
      },

      handleCmptConfigVisualize: function (row) {
        const self = this
        const env = process.env.ENV
        if (env === 'production') {
          self.$Message.warning('正式环境暂不支持');
          return;
        }
        let group = this.getGroup(row.actId)
        if (env === 'development') {
          let port = group + 7035;
          var url = 'http://127.0.0.1:' + port + '/inner/visualizer/componentViewInput?actId=' +
            row.actId + '&componentId=' + row.componentId + '&componentUseIndex=' + row.componentUseIndex + '&componentName=' + row.moduleName;
          window.open(url, '_blank');
        } else if (env === 'staging') {
          let mainhost;
          if (group === 1) {
            mainhost = 'https://test-activity-ge.yy.com';
          } else {
            mainhost = 'https://test-activity' + group + '-ge.yy.com';
          }
          var url = mainhost + '/inner/visualizer/componentViewInput?actId=' +
            row.actId + '&componentId=' + row.componentId + '&componentUseIndex=' + row.componentUseIndex + '&componentName=' + row.moduleName;
          window.open(url, '_blank');
        }
      },
      getGroup(actId) {
          let group = actId % 10000 / 1000;
        return Math.floor(group);
      },

      queryCmptByActIds() {
        const self = this
        if (self.queryActIdList === '[]') {
          self.$Message.warning('请选择活动')
          return;
      }
        self.queryingCmpt = true
        //读取多个活动的组件
        debugger
        console.log('queryActIdList->' + self.queryActIdList)
        Api.listCmptByActIds(self.queryActIdList).then(
          res => {
            if (res.result === 200) {
              self.multiCopyCmptList = res.data
              if (self.multiCopyCmptList.length === 0) {
                self.$Message.warning("没有查询到数据")
              }
            } else {
              self.$Message.warning(res.reason)
            }
            self.queryingCmpt = false
          }
        ).catch((error) => {
          self.queryingCmpt = false
          self.$Message.error(error.status + "," + error.statusText, 5)
        });
      },

      mutliCopying() {
        const self = this;
        if (self.targetActId === '') {
          self.$Message.warning('请选择目标活动');
          return;
        }
        let arr = self.$refs.multipleTable.selection;
        if (arr == null || arr === '') {
          self.$Message.warning('至少选择一个组件');
          return;
        }
        debugger
        console.log('arr->' + JSON.stringify(arr));

        let req = {
          list: JSON.stringify(arr),
          targetActId: self.targetActId
        }

        Api.multiCopyingCmpt(req).then(
          res => {
            if (res.result === 200) {
              self.$Message.success(res.data)
              self.multiCopyVisible = false;
            } else {
              self.$Message.warning(res.reason)
            }
          }).catch((error) => {
          self.multiCopyVisible = false
          self.$Message.error(error.status + "," + error.statusText, 5)
        });
        self.searchForm.actId = self.targetActId;
        this.onSearch();
      },
      onAutotest(cmpt) {
        this.$refs.autotest.loadAutotestConfig(cmpt.actId, cmpt.componentId, cmpt.componentUseIndex)
      },
      onSynCmptAttrDefine(cmpt) {
        const actId = String(cmpt.actId)
        const group = actId.substring(6, 7)
        this.$confirm('将根据第' + group + '套环境代码重新生成组件属性定义（会覆盖旧的属性定义）', '生成组件属性定义', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          Api.synComponentAttrDefine(cmpt.actId, cmpt.componentId).then((res) => {
            if (res && res.result === 200) {
              this.$message({type: 'success', message: '操作成功'})
              return
            }

            this.$message({type: 'warning', message: res?.reason})
          })
        }).catch(() => {
          this.$message({type: 'info', message: '操作已取消'})
        })
      },
      importJsonConfig({jsonValue}) {
        this.$confirm('导入组件配置？', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          Api.initActComponent(jsonValue).then(res => {
            if (res && res.result === 200) {
              this.$message({type: 'success', message: res.data})
              return
            }

            this.$message({type: 'warning', message: res?.reason})
          })
        }).catch(() => {
          this.$message({type: 'info', message: '操作已取消'})
        })
      }
    }
  }
</script>
